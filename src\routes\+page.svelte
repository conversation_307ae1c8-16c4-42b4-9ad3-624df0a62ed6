<script lang="ts">
	import Slider from '../components/Slider.svelte';
	import TimePerception from '../components/TimePerception.svelte';
	import AllReferences from '../components/AllReferences.svelte';
	import { getTimePerceptionForAge, getTimePerceptionForMonth } from '../lib/timePerceptionData';

	let age: number = 20;
	const totalMonths: number = 1080;
	const monthsPerRow: number = 36;
	const rows: number = 30;

	$: monthsLived = age * 12;
	let selectedMonthIndex: number | null = null;
	$: selectedMonthIndex = selectedMonthIndex;

	// Time perception reactive statements
	$: currentTimePerception = getTimePerceptionForAge(age);
	$: selectedTimePerception = selectedMonthIndex !== null
		? getTimePerceptionForMonth(selectedMonthIndex)
		: currentTimePerception;

	const handleClick = (monthIndex: number) => {
		if (monthIndex === selectedMonthIndex) {
			selectedMonthIndex = null;
			return;
		}
		selectedMonthIndex = monthIndex;
	};
</script>

<!-- Mobile Layout -->
<div class="block lg:hidden">
	<div class="mx-auto flex max-w-6xl flex-col items-center p-4">
		<h1 class="mb-4 text-center text-2xl font-bold">A 90-Year Human Life in Months</h1>

		<div class="mb-6 w-full max-w-md rounded-lg border border-white/20 bg-[#141612] p-4">
			<Slider label="Age" bind:value={age} name="age" min={0} max={90} />
		</div>

		<div class="mb-6 w-full max-w-md rounded-lg border border-white/20 bg-[#141612] p-4">
			<span>Selected Month: </span>
			<b>
				{#if selectedMonthIndex}
					{selectedMonthIndex + 1}
				{:else}
					None
				{/if}
			</b>
		</div>

		<div class="mb-4 flex flex-col gap-[2px]">
			{#each Array(rows) as _, rowIndex}
				<div class="flex gap-[2px]">
					{#each Array(monthsPerRow) as _, colIndex}
						{@const monthIndex = rowIndex * monthsPerRow + colIndex}
						<!-- svelte-ignore a11y-no-noninteractive-tabindex a11y-no-static-element-interactions -->
						<div
							class={`h-1 w-1 cursor-pointer rounded-full transition-all duration-300 sm:h-2 sm:w-2 md:h-3 md:w-3 ${monthIndex >= monthsLived ? 'hover:bg-blue-700' : 'hover:bg-red-800'}`}
							class:bg-red-500={monthIndex < monthsLived}
							class:bg-blue-500={monthIndex >= monthsLived}
							class:bg-red-800={selectedMonthIndex === monthIndex && monthIndex < monthsLived}
							class:bg-blue-700={selectedMonthIndex === monthIndex && monthIndex >= monthsLived}
							on:click={() => handleClick(monthIndex)}
							on:keydown={() => handleClick(monthIndex)}
							aria-label="Month Index"
							tabindex={1}
						></div>
					{/each}
				</div>
			{/each}
		</div>

		<div class="mt-4 text-center text-sm">
			<p class="text-orange-500">Each row is 36 months = 3 years</p>
			<p>Months lived: {monthsLived}</p>
			<p>Months remaining: {totalMonths - monthsLived}</p>
		</div>

		<!-- Time Perception Information -->
		<div class="mt-6 w-full">
			<TimePerception
				timePerceptionData={selectedTimePerception}
				currentAge={age}
				selectedMonth={selectedMonthIndex}
			/>
		</div>

		<!-- Educational Section -->
		<div class="mt-6 w-full rounded-lg border border-white/20 bg-[#141612] p-6">
			<h3 class="mb-4 text-lg font-semibold text-white">🧠 The Science of Time Perception</h3>
			<div class="space-y-4 text-sm text-gray-300">
				<div>
					<h4 class="font-semibold text-blue-400 mb-2">Why Time Feels Faster as We Age:</h4>
					<ul class="space-y-2 ml-4">
						<li class="flex items-start">
							<span class="mr-2 text-blue-400">•</span>
							<span><strong>Proportional Theory:</strong> Each year represents a smaller fraction of your total life experience</span>
						</li>
						<li class="flex items-start">
							<span class="mr-2 text-blue-400">•</span>
							<span><strong>Routine Effect:</strong> Familiar experiences are processed more efficiently by the brain</span>
						</li>
						<li class="flex items-start">
							<span class="mr-2 text-blue-400">•</span>
							<span><strong>Memory Formation:</strong> Fewer novel experiences mean fewer distinct memories to mark time</span>
						</li>
						<li class="flex items-start">
							<span class="mr-2 text-blue-400">•</span>
							<span><strong>Biological Clock:</strong> Our internal circadian rhythms change with age</span>
						</li>
					</ul>
				</div>
				<div class="rounded-md bg-purple-500/10 border border-purple-500/20 p-3">
					<p class="text-purple-300">
						<strong>Fun Fact:</strong> When you're 10 years old, one year is 10% of your entire life.
						When you're 50, one year is only 2% of your life experience!
					</p>
				</div>
			</div>
		</div>

		<!-- References Section -->
		<AllReferences />
	</div>
</div>

<!-- Desktop Layout -->
<div class="hidden lg:block">
	<div class="mx-auto flex max-w-7xl gap-6 p-4">
		<!-- Main Content -->
		<div class="flex-1">
			<h1 class="mb-6 text-center text-3xl font-bold">A 90-Year Human Life in Months</h1>

			<div class="mb-6 flex justify-center gap-6">
				<div class="w-80 rounded-lg border border-white/20 bg-[#141612] p-4">
					<Slider label="Age" bind:value={age} name="age" min={0} max={90} />
				</div>

				<div class="w-60 rounded-lg border border-white/20 bg-[#141612] p-4">
					<span>Selected Month: </span>
					<b>
						{#if selectedMonthIndex}
							{selectedMonthIndex + 1}
						{:else}
							None
						{/if}
					</b>
				</div>
			</div>

			<div class="mb-6 flex justify-center">
				<div class="flex flex-col gap-[2px]">
					{#each Array(rows) as _, rowIndex}
						<div class="flex gap-[2px]">
							{#each Array(monthsPerRow) as _, colIndex}
								{@const monthIndex = rowIndex * monthsPerRow + colIndex}
								<!-- svelte-ignore a11y-no-noninteractive-tabindex a11y-no-static-element-interactions -->
								<div
									class={`h-3 w-3 cursor-pointer rounded-full transition-all duration-300 hover:scale-110 ${monthIndex >= monthsLived ? 'hover:bg-blue-700' : 'hover:bg-red-800'}`}
									class:bg-red-500={monthIndex < monthsLived}
									class:bg-blue-500={monthIndex >= monthsLived}
									class:bg-red-800={selectedMonthIndex === monthIndex && monthIndex < monthsLived}
									class:bg-blue-700={selectedMonthIndex === monthIndex && monthIndex >= monthsLived}
									on:click={() => handleClick(monthIndex)}
									on:keydown={() => handleClick(monthIndex)}
									aria-label="Month Index"
									tabindex={1}
								></div>
							{/each}
						</div>
					{/each}
				</div>
			</div>

			<div class="text-center text-sm">
				<p class="text-orange-500">Each row is 36 months = 3 years</p>
				<p>Months lived: {monthsLived}</p>
				<p>Months remaining: {totalMonths - monthsLived}</p>
			</div>

			<!-- Educational Section -->
			<div class="mt-8 rounded-lg border border-white/20 bg-[#141612] p-6">
				<h3 class="mb-4 text-lg font-semibold text-white">🧠 The Science of Time Perception</h3>
				<div class="space-y-4 text-sm text-gray-300">
					<div>
						<h4 class="font-semibold text-blue-400 mb-2">Why Time Feels Faster as We Age:</h4>
						<ul class="space-y-2 ml-4">
							<li class="flex items-start">
								<span class="mr-2 text-blue-400">•</span>
								<span><strong>Proportional Theory:</strong> Each year represents a smaller fraction of your total life experience</span>
							</li>
							<li class="flex items-start">
								<span class="mr-2 text-blue-400">•</span>
								<span><strong>Routine Effect:</strong> Familiar experiences are processed more efficiently by the brain</span>
							</li>
							<li class="flex items-start">
								<span class="mr-2 text-blue-400">•</span>
								<span><strong>Memory Formation:</strong> Fewer novel experiences mean fewer distinct memories to mark time</span>
							</li>
							<li class="flex items-start">
								<span class="mr-2 text-blue-400">•</span>
								<span><strong>Biological Clock:</strong> Our internal circadian rhythms change with age</span>
							</li>
						</ul>
					</div>
					<div class="rounded-md bg-purple-500/10 border border-purple-500/20 p-3">
						<p class="text-purple-300">
							<strong>Fun Fact:</strong> When you're 10 years old, one year is 10% of your entire life.
							When you're 50, one year is only 2% of your life experience!
						</p>
					</div>
				</div>
			</div>

			<!-- References Section -->
			<div class="mt-8">
				<AllReferences />
			</div>
		</div>

		<!-- Sidebar -->
		<div class="w-96 flex-shrink-0">
			<div class="sticky top-4 space-y-6">
				<!-- Time Perception Information -->
				<TimePerception
					timePerceptionData={selectedTimePerception}
					currentAge={age}
					selectedMonth={selectedMonthIndex}
				/>
			</div>
		</div>
	</div>
</div>
